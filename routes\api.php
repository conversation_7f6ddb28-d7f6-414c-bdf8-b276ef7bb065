<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\AssistantController;
use App\Http\Middleware\ApiErrorHandler;
use App\Http\Middleware\ApiRateLimit;
use App\Http\Middleware\ApiRequestLogger;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| WIDDX AI Assistant API Routes
|--------------------------------------------------------------------------
|
| These routes handle the AI assistant functionality including query
| processing, conversation management, and knowledge base operations.
|
*/

// Apply middleware to all AI assistant routes
Route::middleware([ApiRequestLogger::class, ApiErrorHandler::class])->group(function () {

    // Main assistant endpoint with rate limiting
    Route::post('/ask', [AssistantController::class, 'ask'])
        ->middleware([ApiRateLimit::class . ':30,1']) // 30 requests per minute
        ->name('assistant.ask');

    // Conversation management endpoints with lighter rate limiting
    Route::middleware([ApiRateLimit::class . ':100,1'])->group(function () { // 100 requests per minute

        Route::get('/conversations', [AssistantController::class, 'getConversations'])
            ->name('conversations.index');

        Route::get('/conversations/{conversationId}', [AssistantController::class, 'getConversation'])
            ->name('conversations.show')
            ->where('conversationId', '[0-9]+');

        Route::delete('/conversations/{conversationId}', [AssistantController::class, 'deleteConversation'])
            ->name('conversations.destroy')
            ->where('conversationId', '[0-9]+');

        Route::patch('/conversations/{conversationId}/title', [AssistantController::class, 'updateConversationTitle'])
            ->name('conversations.update-title')
            ->where('conversationId', '[0-9]+');
    });
});

// Health check endpoint
Route::get('/health', function () {
    return response()->json([
        'status' => 'ok',
        'timestamp' => now()->toISOString(),
        'version' => '1.0.0',
        'services' => [
            'database' => 'ok',
            'cache' => 'ok',
        ],
    ]);
})->name('health');

// API information endpoint
Route::get('/info', function () {
    return response()->json([
        'name' => 'WIDDX AI Assistant API',
        'version' => '1.0.0',
        'description' => 'Intelligent AI assistant that routes queries to specialized AI models',
        'endpoints' => [
            'POST /api/ask' => 'Send a query to the AI assistant',
            'GET /api/health' => 'Check API health status',
            'GET /api/info' => 'Get API information',
        ],
        'supported_services' => [
            'deepseek' => 'Code generation and logic',
            'gemini' => 'Research and data extraction',
            'huggingface' => 'NLP and creative tasks',
        ],
        'features' => [
            'intelligent_routing',
            'knowledge_base_search',
            'conversation_management',
            'fallback_strategies',
        ],
    ]);
})->name('info');
