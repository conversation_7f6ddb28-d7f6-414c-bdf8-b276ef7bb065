<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Default AI Service
    |--------------------------------------------------------------------------
    |
    | This option controls the default AI service that will be used when the
    | framework needs to use an AI service. This will be used when no specific
    | service is requested by the decision engine.
    |
    */
    'default_service' => env('DEFAULT_AI_SERVICE', 'deepseek'),

    /*
    |--------------------------------------------------------------------------
    | AI Services Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure the AI services used by your application.
    | Each service can be enabled/disabled and configured with API keys,
    | endpoints, and model-specific options.
    |
    */
    'services' => [
        'deepseek' => [
            'enabled' => env('DEEPSEEK_ENABLED', true),
            'api_key' => env('DEEPSEEK_API_KEY'),
            'api_url' => env('DEEPSEEK_API_URL', 'https://api.deepseek.com/v1/chat/completions'),
            'model' => env('DEEPSEEK_MODEL', 'deepseek-chat'),
            'options' => [
                'temperature' => (float) env('DEEPSEEK_TEMPERATURE', 0.7),
                'max_tokens' => (int) env('DEEPSEEK_MAX_TOKENS', 1000),
                'timeout' => (int) env('DEEPSEEK_TIMEOUT', 30),
            ],
        ],
        
        'gemini' => [
            'enabled' => env('GEMINI_ENABLED', true),
            'api_key' => env('GEMINI_API_KEY'),
            'api_url' => env('GEMINI_API_URL', 'https://generativelanguage.googleapis.com/v1beta/models'),
            'model' => env('GEMINI_MODEL', 'gemini-pro'),
            'options' => [
                'temperature' => (float) env('GEMINI_TEMPERATURE', 0.7),
                'max_tokens' => (int) env('GEMINI_MAX_TOKENS', 1000),
                'timeout' => (int) env('GEMINI_TIMEOUT', 30),
            ],
        ],
        
        'huggingface' => [
            'enabled' => env('HUGGINGFACE_ENABLED', true),
            'api_key' => env('HUGGINGFACE_API_KEY'),
            'api_url' => env('HUGGINGFACE_API_URL', 'https://api-inference.huggingface.co/models'),
            'model' => env('HUGGINGFACE_MODEL', 'mistralai/Mistral-7B-Instruct-v0.2'),
            'options' => [
                'temperature' => (float) env('HUGGINGFACE_TEMPERATURE', 0.7),
                'max_tokens' => (int) env('HUGGINGFACE_MAX_TOKENS', 1000),
                'timeout' => (int) env('HUGGINGFACE_TIMEOUT', 30),
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Query Routing Configuration
    |--------------------------------------------------------------------------
    |
    | This section defines the rules used by the decision engine to route
    | queries to the appropriate AI service based on keywords and patterns.
    |
    */
    'routing' => [
        'code' => [
            'service' => 'deepseek',
            'keywords' => ['code', 'function', 'class', 'programming', 'algorithm', 'debug'],
            'patterns' => [
                '/```[a-z]*\n[\s\S]*?\n```/', // Code blocks
                '/function\s+\w+\s*\(/', // Function definitions
                '/class\s+\w+/', // Class definitions
            ],
        ],
        'research' => [
            'service' => 'gemini',
            'keywords' => ['research', 'information', 'data', 'analysis', 'explain', 'what is'],
            'patterns' => [
                '/what\s+is\s+/', // What is questions
                '/how\s+does\s+/', // How does questions
                '/can\s+you\s+explain/', // Explanation requests
            ],
        ],
        'creative' => [
            'service' => 'huggingface',
            'keywords' => ['write', 'create', 'story', 'poem', 'creative', 'sentiment'],
            'patterns' => [
                '/write\s+a\s+(story|poem|song|novel|script)/', // Write a story/poem/etc
                '/create\s+a\s+(story|poem|song|novel|script)/', // Create a story/poem/etc
                '/sentiment\s+of\s+/', // Sentiment analysis
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Fallback Strategy
    |--------------------------------------------------------------------------
    |
    | Configure the fallback strategy when a service is unavailable.
    | The order defines the priority of fallback services.
    |
    */
    'fallback' => [
        'enabled' => env('AI_FALLBACK_ENABLED', true),
        'order' => ['deepseek', 'gemini', 'huggingface'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    |
    | Configure rate limiting for AI service calls to prevent excessive usage.
    |
    */
    'rate_limits' => [
        'enabled' => env('AI_RATE_LIMIT_ENABLED', true),
        'max_requests' => (int) env('AI_RATE_LIMIT_MAX', 60),
        'period_minutes' => (int) env('AI_RATE_LIMIT_PERIOD', 60),
    ],
];