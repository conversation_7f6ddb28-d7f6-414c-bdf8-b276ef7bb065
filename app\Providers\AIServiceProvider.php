<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use App\Services\AI\Contracts\AIServiceInterface;

class AIServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register the decision engine as a singleton
        $this->app->singleton('ai.decision-engine', function ($app) {
            $config = $app->make('config');
            return new \App\Services\DecisionEngine(
                $config->get('ai.routing', [])
            );
        });

        // Register AI services based on configuration
        $this->registerAIServices();
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if (method_exists($this->app, 'configPath')) {
            // Publish configuration file
            $this->publishes([
                __DIR__.'/../../config/ai.php' => config_path('ai.php'),
            ], 'config');
        }
    }

    /**
     * Register AI services based on configuration
     */
    protected function registerAIServices(): void
    {
        $config = $this->app->make('config');

        // Register DeepSeek service if enabled
        if ($config->get('ai.services.deepseek.enabled', false)) {
            $this->app->bind('ai.service.deepseek', function ($app) use ($config) {
                return new \App\Services\AI\DeepSeekService(
                    $config->get('ai.services.deepseek.api_key'),
                    $config->get('ai.services.deepseek.api_url'),
                    $config->get('ai.services.deepseek.model'),
                    $config->get('ai.services.deepseek.options', [])
                );
            });
        }

        // Register Gemini service if enabled
        if ($config->get('ai.services.gemini.enabled', false)) {
            $this->app->bind('ai.service.gemini', function ($app) use ($config) {
                return new \App\Services\AI\GeminiService(
                    $config->get('ai.services.gemini.api_key'),
                    $config->get('ai.services.gemini.api_url'),
                    $config->get('ai.services.gemini.model'),
                    $config->get('ai.services.gemini.options', [])
                );
            });
        }

        // Register HuggingFace service if enabled
        if ($config->get('ai.services.huggingface.enabled', false)) {
            $this->app->bind('ai.service.huggingface', function ($app) use ($config) {
                return new \App\Services\AI\HuggingFaceService(
                    $config->get('ai.services.huggingface.api_key'),
                    $config->get('ai.services.huggingface.api_url'),
                    $config->get('ai.services.huggingface.model'),
                    $config->get('ai.services.huggingface.options', [])
                );
            });
        }

        // Register the default AI service based on configuration
        $this->app->bind(AIServiceInterface::class, function ($app) use ($config) {
            $defaultService = $config->get('ai.default_service', 'deepseek');
            return $app->make("ai.service.$defaultService");
        });
    }
}