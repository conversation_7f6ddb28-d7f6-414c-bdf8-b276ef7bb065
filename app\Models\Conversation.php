<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Conversation extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'title',
    ];

    /**
     * Get the messages for the conversation.
     */
    public function messages(): HasMany
    {
        return $this->hasMany(Message::class)->orderBy('created_at');
    }

    /**
     * Generate a title for the conversation based on the first message.
     *
     * @param string $firstMessage
     * @return string
     */
    public function generateTitle(string $firstMessage): string
    {
        // Truncate and clean the message to create a title
        $title = substr(trim($firstMessage), 0, 40);
        
        // Add ellipsis if the message was truncated
        if (strlen($firstMessage) > 40) {
            $title .= '...';
        }
        
        $this->title = $title;
        $this->save();
        
        return $this->title;
    }

    /**
     * Get the latest message in the conversation.
     *
     * @return \App\Models\Message|null
     */
    public function getLatestMessage()
    {
        return $this->messages()->latest('id')->first();
    }

    /**
     * Get the conversation context as an array of messages.
     *
     * @param int $limit Maximum number of messages to include
     * @return array
     */
    public function getContextMessages(int $limit = 10): array
    {
        return $this->messages()
            ->latest()
            ->take($limit)
            ->orderBy('created_at')
            ->get()
            ->map(function ($message) {
                return [
                    'role' => $message->role,
                    'content' => $message->content,
                ];
            })
            ->toArray();
    }
}